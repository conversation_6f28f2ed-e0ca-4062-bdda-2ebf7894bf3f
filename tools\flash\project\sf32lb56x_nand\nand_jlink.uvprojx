<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_projx.xsd">

  <SchemaVersion>2.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>nand3</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>6140001::V6.14.1::ARMCLANG</pCCUsed>
      <uAC6>1</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>ARMCM33_DSP_FP</Device>
          <Vendor>ARM</Vendor>
          <PackID>ARM.CMSIS.5.7.0</PackID>
          <PackURL>http://www.keil.com/pack/</PackURL>
          <Cpu>IRAM(0x20000000,0x00020000) IRAM2(0x20200000,0x00020000) IROM(0x00000000,0x00200000) IROM2(0x00200000,0x00200000) CPUTYPE("Cortex-M33") FPU3(SFPU) DSP CLOCK(12000000) ESEL ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2V8M(-S0 -C0 -P0 -********** -FC1000)</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:ARMCM33_DSP_FP$Device\ARM\ARMCM33\Include\ARMCM33_DSP_FP.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile></SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\build\keil\Obj\</OutputDirectory>
          <OutputName>bf0</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>0</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>0</BrowseInformation>
          <ListingPath>.\build\keil\List\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>1</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name>.\prebuild.bat</UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>1</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name>cmd.exe /C copy "build\keil\obj\%L" "..\..\jlink_drv\sf32lb56x_nand\SF32LB56X_EXT_NAND3.elf"</UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>1</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName></SimDllName>
          <SimDllArguments></SimDllArguments>
          <SimDlgDll></SimDlgDll>
          <SimDlgDllArguments></SimDlgDllArguments>
          <TargetDllName>SARMV8M.DLL</TargetDllName>
          <TargetDllArguments> -MPU</TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM33</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4101</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2V8M.DLL</Flash2>
          <Flash3>"" ()</Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M33"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>2</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <RvdsCdeCp>0</RvdsCdeCp>
            <hadIRAM2>1</hadIRAM2>
            <hadIROM2>1</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>1</useUlib>
            <EndSel>1</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>4</RoSelD>
            <RwSelD>4</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x200000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x200000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x200000</StartAddress>
                <Size>0x200000</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x20200000</StartAddress>
                <Size>0x20000</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>7</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>0</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>3</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>1</uC99>
            <uGnu>0</uGnu>
            <useXO>0</useXO>
            <v6Lang>3</v6Lang>
            <v6LangP>3</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls>-march=armv8-m.main+cdecp1</MiscControls>
              <Define>SOC_BF0_HCPU, USE_HAL_DRIVER, SF32LB56X, ARM_MATH_LOOPUNROLL,JLINK,JLINK_FLASH_3,_USE_PRODUCTLINE,HAL_TICK_PER_SECOND=1000,BSP_USING_BBM,PAGE_SIZE=131072,HAL_USE_NAND,CFG_FACTORY_DEBUG</Define>
              <Undefine></Undefine>
              <IncludePath>.;.\src;..\..\..\..\customer\boards\butterflite_fpga;..\..\..\..\customer\boards\include;..\..\..\..\drivers\cmsis\sf32lb56x;..\..\..\..\drivers\cmsis\Include;..\..\..\..\external\CMSIS\Include;..\..\..\..\drivers\Include;..\..\..\..\rtos\rtthread\bsp\sifli\drivers\config\sf32lb56x;..\..\..\..\customer\boards\include\config\sf32lb56x;..\..\src</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <ClangAsOpt>2</ClangAsOpt>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>0</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x00000000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile>../../link_scripts/link_jlink_nand.sct</ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc>--cpu=Cortex-M33 --strict --scatter ../../link_scripts/link_jlink_nand.sct --summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols --info sizes --info totals --info unused --info veneers  --list build/bf0_ap.map  --libpath=C:/Keil_v5/ARM/ARMCLANG/lib --library_type=microlib</Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>Applications</GroupName>
          <Files>
            <File>
              <FileName>FlashDev.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\src\FlashDev.c</FilePath>
            </File>
            <File>
              <FileName>FlashPrg.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\src\FlashPrg.c</FilePath>
            </File>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\src\main.c</FilePath>
            </File>
            <File>
              <FileName>user_cfg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\user_cfg.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>CMSIS_BF0</GroupName>
          <Files>
            <File>
              <FileName>bf0_pin_const.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\cmsis\sf32lb56x\bf0_pin_const.c</FilePath>
            </File>
            <File>
              <FileName>flash_table.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\flash_table.c</FilePath>
            </File>
            <File>
              <FileName>nand_table.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\nand_table.c</FilePath>
            </File>
            <File>
              <FileName>startup_bf0_hcpu_ac6.S</FileName>
              <FileType>2</FileType>
              <FilePath>..\..\..\..\drivers\cmsis\sf32lb56x\Templates\arm\startup_bf0_hcpu_ac6.S</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>BF0_HAL</GroupName>
          <Files>
            <File>
              <FileName>bf0_hal.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_cortex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_cortex.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_dma.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_pinmux.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_pinmux.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_uart.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_rcc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_rcc.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_hpaon.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_hpaon.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_pmu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_pmu.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_gpio.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_lpaon.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_lpaon.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_mpi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_mpi.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_mpi_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_mpi_ex.c</FilePath>
            </File>
            <File>
              <FileName>sifli_bbm.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\sifli_bbm.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_rtc.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_ext_dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_ext_dma.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_hlp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_hlp.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>PMIC</GroupName>
          <Files>
            <File>
              <FileName>pmic_controller.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\src\pmic_controller.c</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
    <Target>
      <TargetName>nand3_bigblk</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>6140001::V6.14.1::ARMCLANG</pCCUsed>
      <uAC6>1</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>ARMCM33_DSP_FP</Device>
          <Vendor>ARM</Vendor>
          <PackID>ARM.CMSIS.5.7.0</PackID>
          <PackURL>http://www.keil.com/pack/</PackURL>
          <Cpu>IRAM(0x20000000,0x00020000) IRAM2(0x20200000,0x00020000) IROM(0x00000000,0x00200000) IROM2(0x00200000,0x00200000) CPUTYPE("Cortex-M33") FPU3(SFPU) DSP CLOCK(12000000) ESEL ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2V8M(-S0 -C0 -P0 -********** -FC1000)</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:ARMCM33_DSP_FP$Device\ARM\ARMCM33\Include\ARMCM33_DSP_FP.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile></SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\build\keil\Obj\</OutputDirectory>
          <OutputName>bf0</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>0</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>0</BrowseInformation>
          <ListingPath>.\build\keil\List\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>1</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name>.\prebuild.bat</UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>1</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name>cmd.exe /C copy "build\keil\obj\%L" "..\..\jlink_drv\sf32lb56x_nand\SF32LB56X_EXT_BIGBLK_NAND3.elf"</UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>1</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName></SimDllName>
          <SimDllArguments></SimDllArguments>
          <SimDlgDll></SimDlgDll>
          <SimDlgDllArguments></SimDlgDllArguments>
          <TargetDllName>SARMV8M.DLL</TargetDllName>
          <TargetDllArguments> -MPU</TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM33</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4101</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2V8M.DLL</Flash2>
          <Flash3>"" ()</Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M33"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>2</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <RvdsCdeCp>0</RvdsCdeCp>
            <hadIRAM2>1</hadIRAM2>
            <hadIROM2>1</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>1</useUlib>
            <EndSel>1</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>4</RoSelD>
            <RwSelD>4</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x200000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x200000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x200000</StartAddress>
                <Size>0x200000</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x20200000</StartAddress>
                <Size>0x20000</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>7</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>0</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>3</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>1</uC99>
            <uGnu>0</uGnu>
            <useXO>0</useXO>
            <v6Lang>3</v6Lang>
            <v6LangP>3</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls>-march=armv8-m.main+cdecp1</MiscControls>
              <Define>SOC_BF0_HCPU, USE_HAL_DRIVER, SF32LB56X, ARM_MATH_LOOPUNROLL,JLINK,JLINK_FLASH_3,_USE_PRODUCTLINE,HAL_TICK_PER_SECOND=1000,BSP_USING_BBM,PAGE_SIZE=262144,HAL_USE_NAND,CFG_FACTORY_DEBUG</Define>
              <Undefine></Undefine>
              <IncludePath>.;.\src;..\..\..\..\customer\boards\butterflite_fpga;..\..\..\..\customer\boards\include;..\..\..\..\drivers\cmsis\sf32lb56x;..\..\..\..\drivers\cmsis\Include;..\..\..\..\external\CMSIS\Include;..\..\..\..\drivers\Include;..\..\..\..\rtos\rtthread\bsp\sifli\drivers\config\sf32lb56x;..\..\..\..\customer\boards\include\config\sf32lb56x;..\..\src</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <ClangAsOpt>2</ClangAsOpt>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>0</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x00000000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile>../../link_scripts/link_jlink_nand.sct</ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc>--cpu=Cortex-M33 --strict --scatter ../../link_scripts/link_jlink_nand.sct --summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols --info sizes --info totals --info unused --info veneers  --list build/bf0_ap.map  --libpath=C:/Keil_v5/ARM/ARMCLANG/lib --library_type=microlib</Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>Applications</GroupName>
          <Files>
            <File>
              <FileName>FlashDev.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\src\FlashDev.c</FilePath>
            </File>
            <File>
              <FileName>FlashPrg.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\src\FlashPrg.c</FilePath>
            </File>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\src\main.c</FilePath>
            </File>
            <File>
              <FileName>user_cfg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\user_cfg.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>CMSIS_BF0</GroupName>
          <Files>
            <File>
              <FileName>bf0_pin_const.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\cmsis\sf32lb56x\bf0_pin_const.c</FilePath>
            </File>
            <File>
              <FileName>flash_table.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\flash_table.c</FilePath>
            </File>
            <File>
              <FileName>nand_table.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\nand_table.c</FilePath>
            </File>
            <File>
              <FileName>startup_bf0_hcpu_ac6.S</FileName>
              <FileType>2</FileType>
              <FilePath>..\..\..\..\drivers\cmsis\sf32lb56x\Templates\arm\startup_bf0_hcpu_ac6.S</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>BF0_HAL</GroupName>
          <Files>
            <File>
              <FileName>bf0_hal.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_cortex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_cortex.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_dma.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_pinmux.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_pinmux.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_uart.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_rcc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_rcc.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_hpaon.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_hpaon.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_pmu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_pmu.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_gpio.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_lpaon.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_lpaon.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_mpi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_mpi.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_mpi_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_mpi_ex.c</FilePath>
            </File>
            <File>
              <FileName>sifli_bbm.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\sifli_bbm.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_rtc.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_ext_dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_ext_dma.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_hlp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_hlp.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>PMIC</GroupName>
          <Files>
            <File>
              <FileName>pmic_controller.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\src\pmic_controller.c</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
    <Target>
      <TargetName>nand3_nobbm</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>6140001::V6.14.1::ARMCLANG</pCCUsed>
      <uAC6>1</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>ARMCM33_DSP_FP</Device>
          <Vendor>ARM</Vendor>
          <PackID>ARM.CMSIS.5.7.0</PackID>
          <PackURL>http://www.keil.com/pack/</PackURL>
          <Cpu>IRAM(0x20000000,0x00020000) IRAM2(0x20200000,0x00020000) IROM(0x00000000,0x00200000) IROM2(0x00200000,0x00200000) CPUTYPE("Cortex-M33") FPU3(SFPU) DSP CLOCK(12000000) ESEL ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2V8M(-S0 -C0 -P0 -********** -FC1000)</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:ARMCM33_DSP_FP$Device\ARM\ARMCM33\Include\ARMCM33_DSP_FP.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile></SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\build\keil\Obj\</OutputDirectory>
          <OutputName>bf0</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>0</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>0</BrowseInformation>
          <ListingPath>.\build\keil\List\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>1</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name>.\prebuild.bat</UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>1</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name>cmd.exe /C copy "build\keil\obj\%L" "..\..\jlink_drv\sf32lb56x_nand\SF32LB56X_EXT_ORG_NAND3.elf"</UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>1</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName></SimDllName>
          <SimDllArguments></SimDllArguments>
          <SimDlgDll></SimDlgDll>
          <SimDlgDllArguments></SimDlgDllArguments>
          <TargetDllName>SARMV8M.DLL</TargetDllName>
          <TargetDllArguments> -MPU</TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM33</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4101</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2V8M.DLL</Flash2>
          <Flash3>"" ()</Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M33"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>2</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <RvdsCdeCp>0</RvdsCdeCp>
            <hadIRAM2>1</hadIRAM2>
            <hadIROM2>1</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>1</useUlib>
            <EndSel>1</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>4</RoSelD>
            <RwSelD>4</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x200000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x200000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x200000</StartAddress>
                <Size>0x200000</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x20200000</StartAddress>
                <Size>0x20000</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>7</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>0</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>3</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>1</uC99>
            <uGnu>0</uGnu>
            <useXO>0</useXO>
            <v6Lang>3</v6Lang>
            <v6LangP>3</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls>-march=armv8-m.main+cdecp1</MiscControls>
              <Define>SOC_BF0_HCPU, USE_HAL_DRIVER, SF32LB56X, ARM_MATH_LOOPUNROLL,JLINK,JLINK_FLASH_3,_USE_PRODUCTLINE,HAL_TICK_PER_SECOND=1000,PAGE_SIZE=131072,HAL_USE_NAND,CFG_FACTORY_DEBUG</Define>
              <Undefine></Undefine>
              <IncludePath>.;.\src;..\..\..\..\customer\boards\butterflite_fpga;..\..\..\..\customer\boards\include;..\..\..\..\drivers\cmsis\sf32lb56x;..\..\..\..\drivers\cmsis\Include;..\..\..\..\external\CMSIS\Include;..\..\..\..\drivers\Include;..\..\..\..\rtos\rtthread\bsp\sifli\drivers\config\sf32lb56x;..\..\..\..\customer\boards\include\config\sf32lb56x;..\..\src</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <ClangAsOpt>2</ClangAsOpt>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>0</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x00000000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile>../../link_scripts/link_jlink_nand.sct</ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc>--cpu=Cortex-M33 --strict --scatter ../../link_scripts/link_jlink_nand.sct --summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols --info sizes --info totals --info unused --info veneers  --list build/bf0_ap.map  --libpath=C:/Keil_v5/ARM/ARMCLANG/lib --library_type=microlib</Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>Applications</GroupName>
          <Files>
            <File>
              <FileName>FlashDev.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\src\FlashDev.c</FilePath>
            </File>
            <File>
              <FileName>FlashPrg.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\src\FlashPrg.c</FilePath>
            </File>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\src\main.c</FilePath>
            </File>
            <File>
              <FileName>user_cfg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\user_cfg.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>CMSIS_BF0</GroupName>
          <Files>
            <File>
              <FileName>bf0_pin_const.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\cmsis\sf32lb56x\bf0_pin_const.c</FilePath>
            </File>
            <File>
              <FileName>flash_table.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\flash_table.c</FilePath>
            </File>
            <File>
              <FileName>nand_table.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\nand_table.c</FilePath>
            </File>
            <File>
              <FileName>startup_bf0_hcpu_ac6.S</FileName>
              <FileType>2</FileType>
              <FilePath>..\..\..\..\drivers\cmsis\sf32lb56x\Templates\arm\startup_bf0_hcpu_ac6.S</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>BF0_HAL</GroupName>
          <Files>
            <File>
              <FileName>bf0_hal.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_cortex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_cortex.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_dma.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_pinmux.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_pinmux.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_uart.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_rcc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_rcc.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_hpaon.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_hpaon.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_pmu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_pmu.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_gpio.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_lpaon.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_lpaon.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_mpi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_mpi.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_mpi_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_mpi_ex.c</FilePath>
            </File>
            <File>
              <FileName>sifli_bbm.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\sifli_bbm.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_rtc.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_ext_dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_ext_dma.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_hlp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_hlp.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>PMIC</GroupName>
          <Files>
            <File>
              <FileName>pmic_controller.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\src\pmic_controller.c</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
    <Target>
      <TargetName>nand3_nobbm_bigblk</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>6140001::V6.14.1::ARMCLANG</pCCUsed>
      <uAC6>1</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>ARMCM33_DSP_FP</Device>
          <Vendor>ARM</Vendor>
          <PackID>ARM.CMSIS.5.7.0</PackID>
          <PackURL>http://www.keil.com/pack/</PackURL>
          <Cpu>IRAM(0x20000000,0x00020000) IRAM2(0x20200000,0x00020000) IROM(0x00000000,0x00200000) IROM2(0x00200000,0x00200000) CPUTYPE("Cortex-M33") FPU3(SFPU) DSP CLOCK(12000000) ESEL ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2V8M(-S0 -C0 -P0 -********** -FC1000)</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:ARMCM33_DSP_FP$Device\ARM\ARMCM33\Include\ARMCM33_DSP_FP.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile></SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\build\keil\Obj\</OutputDirectory>
          <OutputName>bf0</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>0</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>0</BrowseInformation>
          <ListingPath>.\build\keil\List\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>1</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name>.\prebuild.bat</UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>1</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name>cmd.exe /C copy "build\keil\obj\%L" "..\..\jlink_drv\sf32lb56x_nand\SF32LB56X_EXT_ORG_BIGBLK_NAND3.elf"</UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>1</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName></SimDllName>
          <SimDllArguments></SimDllArguments>
          <SimDlgDll></SimDlgDll>
          <SimDlgDllArguments></SimDlgDllArguments>
          <TargetDllName>SARMV8M.DLL</TargetDllName>
          <TargetDllArguments> -MPU</TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM33</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4101</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2V8M.DLL</Flash2>
          <Flash3>"" ()</Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M33"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>2</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <RvdsCdeCp>0</RvdsCdeCp>
            <hadIRAM2>1</hadIRAM2>
            <hadIROM2>1</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>1</useUlib>
            <EndSel>1</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>4</RoSelD>
            <RwSelD>4</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x200000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x200000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x200000</StartAddress>
                <Size>0x200000</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x20200000</StartAddress>
                <Size>0x20000</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>7</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>0</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>3</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>1</uC99>
            <uGnu>0</uGnu>
            <useXO>0</useXO>
            <v6Lang>3</v6Lang>
            <v6LangP>3</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls>-march=armv8-m.main+cdecp1</MiscControls>
              <Define>SOC_BF0_HCPU, USE_HAL_DRIVER, SF32LB56X, ARM_MATH_LOOPUNROLL,JLINK,JLINK_FLASH_3,_USE_PRODUCTLINE,HAL_TICK_PER_SECOND=1000,PAGE_SIZE=262144,HAL_USE_NAND,CFG_FACTORY_DEBUG</Define>
              <Undefine></Undefine>
              <IncludePath>.;.\src;..\..\..\..\customer\boards\butterflite_fpga;..\..\..\..\customer\boards\include;..\..\..\..\drivers\cmsis\sf32lb56x;..\..\..\..\drivers\cmsis\Include;..\..\..\..\external\CMSIS\Include;..\..\..\..\drivers\Include;..\..\..\..\rtos\rtthread\bsp\sifli\drivers\config\sf32lb56x;..\..\..\..\customer\boards\include\config\sf32lb56x;..\..\src</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <ClangAsOpt>2</ClangAsOpt>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>0</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x00000000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile>../../link_scripts/link_jlink_nand.sct</ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc>--cpu=Cortex-M33 --strict --scatter ../../link_scripts/link_jlink_nand.sct --summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols --info sizes --info totals --info unused --info veneers  --list build/bf0_ap.map  --libpath=C:/Keil_v5/ARM/ARMCLANG/lib --library_type=microlib</Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>Applications</GroupName>
          <Files>
            <File>
              <FileName>FlashDev.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\src\FlashDev.c</FilePath>
            </File>
            <File>
              <FileName>FlashPrg.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\src\FlashPrg.c</FilePath>
            </File>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\src\main.c</FilePath>
            </File>
            <File>
              <FileName>user_cfg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\user_cfg.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>CMSIS_BF0</GroupName>
          <Files>
            <File>
              <FileName>bf0_pin_const.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\cmsis\sf32lb56x\bf0_pin_const.c</FilePath>
            </File>
            <File>
              <FileName>flash_table.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\flash_table.c</FilePath>
            </File>
            <File>
              <FileName>nand_table.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\nand_table.c</FilePath>
            </File>
            <File>
              <FileName>startup_bf0_hcpu_ac6.S</FileName>
              <FileType>2</FileType>
              <FilePath>..\..\..\..\drivers\cmsis\sf32lb56x\Templates\arm\startup_bf0_hcpu_ac6.S</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>BF0_HAL</GroupName>
          <Files>
            <File>
              <FileName>bf0_hal.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_cortex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_cortex.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_dma.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_pinmux.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_pinmux.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_uart.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_rcc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_rcc.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_hpaon.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_hpaon.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_pmu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_pmu.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_gpio.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_lpaon.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_lpaon.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_mpi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_mpi.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_mpi_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_mpi_ex.c</FilePath>
            </File>
            <File>
              <FileName>sifli_bbm.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\sifli_bbm.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_rtc.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_ext_dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_ext_dma.c</FilePath>
            </File>
            <File>
              <FileName>bf0_hal_hlp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\drivers\hal\bf0_hal_hlp.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>PMIC</GroupName>
          <Files>
            <File>
              <FileName>pmic_controller.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\src\pmic_controller.c</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
  </Targets>

  <RTE>
    <apis/>
    <components/>
    <files/>
  </RTE>

  <LayerInfo>
    <Layers>
      <Layer>
        <LayName>nand_jlink</LayName>
        <LayPrjMark>1</LayPrjMark>
      </Layer>
    </Layers>
  </LayerInfo>

</Project>
