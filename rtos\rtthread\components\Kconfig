menu "RT-Thread Components"

config RT_USING_COMPONENTS_INIT
    bool "Use components automatically initialization"
    default y

if RT_USING_COMPONENTS_INIT
    config RT_USING_USER_MAIN
        bool "The main() function as user entry function"
        default y

    if RT_USING_USER_MAIN
        config RT_MAIN_THREAD_STACK_SIZE
            int "Set main thread stack size"
            default 2048
        config RT_MAIN_THREAD_PRIORITY
            int "Set main thread priority" 
            default 4  if RT_THREAD_PRIORITY_8
            default 10  if RT_THREAD_PRIORITY_32
            default 85  if RT_THREAD_PRIORITY_256
    endif
endif

source "$SIFLI_SDK/rtos/rtthread/components/cplusplus/Kconfig"

source "$SIFLI_SDK/rtos/rtthread/components/finsh/Kconfig"

source "$SIFLI_SDK/rtos/rtthread/components/dfs/Kconfig"

source "$SIFLI_SDK/rtos/rtthread/components/drivers/Kconfig"

source "$SIFLI_SDK/rtos/rtthread/components/libc/Kconfig"

source "$SIFLI_SDK/rtos/rtthread/components/net/Kconfig"

source "$SIFLI_SDK/rtos/rtthread/components/utilities/Kconfig"

endmenu
