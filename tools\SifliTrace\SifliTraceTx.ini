[TX]
MSG_NUM=199

MSG_0=help
MSG_0_SEL=0
MSG_0_DELAY=
MSG_0_TYPE=

MSG_1=ls
MSG_1_SEL=0
MSG_1_DELAY=
MSG_1_TYPE=

MSG_2=fps
MSG_2_SEL=0
MSG_2_DELAY=
MSG_2_TYPE=

MSG_3=version
MSG_3_SEL=0
MSG_3_DELAY=
MSG_3_TYPE=

MSG_4=sysinfo
MSG_4_SEL=0
MSG_4_DELAY=
MSG_4_TYPE=

MSG_5=assert
MSG_5_SEL=0
MSG_5_DELAY=
MSG_5_TYPE=

MSG_6=free
MSG_6_SEL=0
MSG_6_DELAY=
MSG_6_TYPE=

MSG_7=list_app
MSG_7_SEL=0
MSG_7_DELAY=
MSG_7_TYPE=

MSG_8=list_symbols
MSG_8_SEL=0
MSG_8_DELAY=
MSG_8_TYPE=

MSG_9=list_module
MSG_9_SEL=0
MSG_9_DELAY=
MSG_9_TYPE=

MSG_10=list_module_sym
MSG_10_SEL=0
MSG_10_DELAY=
MSG_10_TYPE=

MSG_11=show_date
MSG_11_SEL=0
MSG_11_DELAY=
MSG_11_TYPE=

MSG_12=utest_list
MSG_12_SEL=0
MSG_12_DELAY=
MSG_12_TYPE=

MSG_13=mkfs
MSG_13_SEL=0
MSG_13_DELAY=
MSG_13_TYPE=

MSG_14=df
MSG_14_SEL=0
MSG_14_DELAY=
MSG_14_TYPE=

MSG_15=rm
MSG_15_SEL=0
MSG_15_DELAY=
MSG_15_TYPE=

MSG_16=mem_usage
MSG_16_SEL=0
MSG_16_DELAY=
MSG_16_TYPE=

MSG_17=app_mem
MSG_17_SEL=0
MSG_17_DELAY=
MSG_17_TYPE=

MSG_18=otp_factory_read
MSG_18_SEL=0
MSG_18_DELAY=
MSG_18_TYPE=

MSG_19=
MSG_19_SEL=0
MSG_19_DELAY=
MSG_19_TYPE=

MSG_20=<< pin set cmd >>
MSG_20_SEL=0
MSG_20_DELAY=
MSG_20_TYPE=

MSG_21=pin mode 115 0
MSG_21_SEL=0
MSG_21_DELAY=
MSG_21_TYPE=

MSG_22=pin mux 115 0
MSG_22_SEL=0
MSG_22_DELAY=
MSG_22_TYPE=

MSG_23=pin write 115 1
MSG_23_SEL=0
MSG_23_DELAY=
MSG_23_TYPE=

MSG_24=pin status all
MSG_24_SEL=0
MSG_24_DELAY=
MSG_24_TYPE=

MSG_25=pin status 122
MSG_25_SEL=0
MSG_25_DELAY=
MSG_25_TYPE=

MSG_26=
MSG_26_SEL=0
MSG_26_DELAY=
MSG_26_TYPE=

MSG_27=<< ip/mem rw: hex no 0x, addr(little India) write data big India)  >>
MSG_27_SEL=0
MSG_27_DELAY=
MSG_27_TYPE=

MSG_28=regop unlock 0000
MSG_28_SEL=0
MSG_28_DELAY=
MSG_28_TYPE=

MSG_29=regop read 4004f000 1
MSG_29_SEL=0
MSG_29_DELAY=
MSG_29_TYPE=

MSG_30=regop write 4004f000 00000000
MSG_30_SEL=0
MSG_30_DELAY=
MSG_30_TYPE=

MSG_31=
MSG_31_SEL=0
MSG_31_DELAY=
MSG_31_TYPE=

MSG_32=<< nvds cmd >>
MSG_32_SEL=0
MSG_32_DELAY=
MSG_32_TYPE=

MSG_33=nvds update hci_log 1
MSG_33_SEL=0
MSG_33_DELAY=
MSG_33_TYPE=

MSG_34=nvds update hci_log 0
MSG_34_SEL=0
MSG_34_DELAY=
MSG_34_TYPE=

MSG_35=nvds read 1
MSG_35_SEL=0
MSG_35_DELAY=
MSG_35_TYPE=

MSG_36=nvds get_mac
MSG_36_SEL=0
MSG_36_DELAY=
MSG_36_TYPE=

MSG_37=nvds update addr 6 4441234725C1
MSG_37_SEL=0
MSG_37_DELAY=
MSG_37_TYPE=

MSG_38=nvds reset_all 1
MSG_38_SEL=0
MSG_38_DELAY=
MSG_38_TYPE=

MSG_39=
MSG_39_SEL=0
MSG_39_DELAY=
MSG_39_TYPE=

MSG_40=<< ASSERT/LOG save cmd >>
MSG_40_SEL=0
MSG_40_DELAY=
MSG_40_TYPE=

MSG_41=save_assert 2
MSG_41_SEL=0
MSG_41_DELAY=
MSG_41_TYPE=

MSG_42=save_log 2
MSG_42_SEL=0
MSG_42_DELAY=
MSG_42_TYPE=

MSG_43=save_log 1
MSG_43_SEL=0
MSG_43_DELAY=
MSG_43_TYPE=

MSG_44=
MSG_44_SEL=0
MSG_44_DELAY=
MSG_44_TYPE=

MSG_45=<< monkey test cmd >>
MSG_45_SEL=0
MSG_45_DELAY=
MSG_45_TYPE=

MSG_46=test help
MSG_46_SEL=0
MSG_46_DELAY=
MSG_46_TYPE=

MSG_47=test stop
MSG_47_SEL=0
MSG_47_DELAY=
MSG_47_TYPE=

MSG_48=test random
MSG_48_SEL=0
MSG_48_DELAY=
MSG_48_TYPE=

MSG_49=test random 200 -e
MSG_49_SEL=0
MSG_49_DELAY=
MSG_49_TYPE=

MSG_50=test trav 200
MSG_50_SEL=0
MSG_50_DELAY=
MSG_50_TYPE=

MSG_51=test app Blood_oxygen
MSG_51_SEL=0
MSG_51_DELAY=
MSG_51_TYPE=

MSG_52=test app Clock -period 30 -e
MSG_52_SEL=0
MSG_52_DELAY=
MSG_52_TYPE=

MSG_53=test record 0x64f80000 0x80000
MSG_53_SEL=0
MSG_53_DELAY=
MSG_53_TYPE=

MSG_54=test playback 0x64f80000
MSG_54_SEL=0
MSG_54_DELAY=
MSG_54_TYPE=

MSG_55=test record
MSG_55_SEL=0
MSG_55_DELAY=
MSG_55_TYPE=

MSG_56=test playback
MSG_56_SEL=0
MSG_56_DELAY=
MSG_56_TYPE=

MSG_57=test log_on
MSG_57_SEL=0
MSG_57_DELAY=
MSG_57_TYPE=

MSG_58=test log_off
MSG_58_SEL=0
MSG_58_DELAY=
MSG_58_TYPE=

MSG_59=test -evt click -xy 100 200
MSG_59_SEL=0
MSG_59_DELAY=
MSG_59_TYPE=

MSG_60=test -evt left -interval 50 -duration 50
MSG_60_SEL=0
MSG_60_DELAY=
MSG_60_TYPE=

MSG_61=test -evt up -interval 50 -duration 50
MSG_61_SEL=0
MSG_61_DELAY=
MSG_61_TYPE=

MSG_62=test -evt right -interval 50 -duration 50
MSG_62_SEL=0
MSG_62_DELAY=
MSG_62_TYPE=

MSG_63=test -evt down -interval 50 -duration 50
MSG_63_SEL=0
MSG_63_DELAY=
MSG_63_TYPE=

MSG_64=
MSG_64_SEL=0
MSG_64_DELAY=
MSG_64_TYPE=

MSG_65=<< other test cmd >>
MSG_65_SEL=0
MSG_65_DELAY=
MSG_65_TYPE=

MSG_66=lcpu period awake -period 20
MSG_66_SEL=0
MSG_66_DELAY=
MSG_66_TYPE=

MSG_67=lcpu period awake -period 20 test trav
MSG_67_SEL=0
MSG_67_DELAY=
MSG_67_TYPE=

MSG_68=lcpu_period_awake -period 3000 -low_battery -monkey 15000
MSG_68_SEL=0
MSG_68_DELAY=
MSG_68_TYPE=

MSG_69=lcpu_period_awake -period 20 -low_battery -test trav -period 30 -e
MSG_69_SEL=0
MSG_69_DELAY=
MSG_69_TYPE=

MSG_70=app_message
MSG_70_SEL=0
MSG_70_DELAY=
MSG_70_TYPE=

MSG_71=sms 3000 lala
MSG_71_SEL=0
MSG_71_DELAY=
MSG_71_TYPE=

MSG_72=sms 500
MSG_72_SEL=0
MSG_72_DELAY=
MSG_72_TYPE=

MSG_73=period_msg 10000
MSG_73_SEL=0
MSG_73_DELAY=
MSG_73_TYPE=

MSG_74=notify 500
MSG_74_SEL=0
MSG_74_DELAY=
MSG_74_TYPE=

MSG_75=call 10 15213386350
MSG_75_SEL=0
MSG_75_DELAY=
MSG_75_TYPE=

MSG_76=hangup
MSG_76_SEL=0
MSG_76_DELAY=
MSG_76_TYPE=

MSG_77=lcpu_period_awake
MSG_77_SEL=0
MSG_77_DELAY=
MSG_77_TYPE=

MSG_78=lcpu_period_awake -period 20
MSG_78_SEL=0
MSG_78_DELAY=
MSG_78_TYPE=

MSG_79=lcpu_period_awake -period 60 -low_battery -monkey 30
MSG_79_SEL=0
MSG_79_DELAY=
MSG_79_TYPE=

MSG_80=lcpu_period_awake -period 20 -low_battery -test trav -period 30 -e
MSG_80_SEL=0
MSG_80_DELAY=
MSG_80_TYPE=

MSG_81=lcpu_period_awake
MSG_81_SEL=0
MSG_81_DELAY=
MSG_81_TYPE=

MSG_82=app_bt query
MSG_82_SEL=0
MSG_82_DELAY=
MSG_82_TYPE=

MSG_83=cm_cmd disc
MSG_83_SEL=0
MSG_83_DELAY=
MSG_83_TYPE=

MSG_84=lcpu help
MSG_84_SEL=0
MSG_84_DELAY=
MSG_84_TYPE=

MSG_85=lcpu on
MSG_85_SEL=0
MSG_85_DELAY=
MSG_85_TYPE=

MSG_86=lcpu free
MSG_86_SEL=0
MSG_86_DELAY=
MSG_86_TYPE=

MSG_87=set_sys_time 210925 093000
MSG_87_SEL=0
MSG_87_DELAY=
MSG_87_TYPE=

MSG_88=set_user_info 1 20 175 60 3000 0
MSG_88_SEL=0
MSG_88_DELAY=
MSG_88_TYPE=

MSG_89=set_gesture 0 1
MSG_89_SEL=0
MSG_89_DELAY=
MSG_89_TYPE=

MSG_90=set_seden 1 9 21 1 20
MSG_90_SEL=0
MSG_90_DELAY=
MSG_90_TYPE=

MSG_91=set_sleep_time 22 23
MSG_91_SEL=0
MSG_91_DELAY=
MSG_91_TYPE=

MSG_92=active_get_sleep_data
MSG_92_SEL=0
MSG_92_DELAY=
MSG_92_TYPE=

MSG_93=loc_aud play shuixingji.mp3
MSG_93_SEL=0
MSG_93_DELAY=
MSG_93_TYPE=

MSG_94=loc_aud stop
MSG_94_SEL=0
MSG_94_DELAY=
MSG_94_TYPE=

MSG_95=send message"hello lcpu"
MSG_95_SEL=0
MSG_95_DELAY=
MSG_95_TYPE=

MSG_96=send message"hello hcpu"
MSG_96_SEL=0
MSG_96_DELAY=
MSG_96_TYPE=

MSG_97=rtt_show_address
MSG_97_SEL=0
MSG_97_DELAY=
MSG_97_TYPE=

MSG_98=shutdown
MSG_98_SEL=0
MSG_98_DELAY=
MSG_98_TYPE=

MSG_99=shutdown 10
MSG_99_SEL=0
MSG_99_DELAY=
MSG_99_TYPE=

MSG_100=
MSG_100_SEL=0
MSG_100_DELAY=
MSG_100_TYPE=

MSG_101=01 03 0c 00
MSG_101_SEL=0
MSG_101_DELAY=
MSG_101_TYPE=HEX

MSG_102=MsDelay.exe 1000
MSG_102_SEL=0
MSG_102_DELAY=
MSG_102_TYPE=CMD

MSG_103=
MSG_103_SEL=0
MSG_103_DELAY=
MSG_103_TYPE=

MSG_104=<< demo: read file from dut >>
MSG_104_SEL=0
MSG_104_DELAY=
MSG_104_TYPE=

MSG_105=FsrwTool\FsrwTool.exe --rwtype 0 --port COM7 --baund 1000000 --dutfile ramfs/img_reset.bin  --pcfile d:\21.bin
MSG_105_SEL=0
MSG_105_DELAY=
MSG_105_TYPE=CMD

MSG_106=<< demo: write file to dut >>
MSG_106_SEL=0
MSG_106_DELAY=
MSG_106_TYPE=

MSG_107=FsrwTool\FsrwTool.exe --rwtype 1 --port COM7 --baund 1000000 --dutfile ramfs/ttt.bin  --pcfile d:\21.bin
MSG_107_SEL=0
MSG_107_DELAY=
MSG_107_TYPE=CMD

MSG_108=<< demo: read framebuf from dut >>
MSG_108_SEL=0
MSG_108_DELAY=
MSG_108_TYPE=

MSG_109=FsrwTool\FsrwTool.exe --rwtype 2 --port COM7 --baund 1000000 --pcfile d:\framebuf.bin
MSG_109_SEL=0
MSG_109_DELAY=
MSG_109_TYPE=CMD

MSG_110=<< demo: translate framebuf to bmp >>
MSG_110_SEL=0
MSG_110_DELAY=
MSG_110_TYPE=

MSG_111=bin2bmp\bin2bmp.exe d:\framebuf.bin rgb565 368 448 1
MSG_111_SEL=0
MSG_111_DELAY=
MSG_111_TYPE=CMD

MSG_112=
MSG_112_SEL=0
MSG_112_DELAY=
MSG_112_TYPE=

MSG_113=<< ble ADV/CONN cmd>>
MSG_113_SEL=0
MSG_113_DELAY=
MSG_113_TYPE=

MSG_114=ble_config adv 50
MSG_114_SEL=0
MSG_114_DELAY=
MSG_114_TYPE=

MSG_115=ble_config adv 100
MSG_115_SEL=0
MSG_115_DELAY=
MSG_115_TYPE=

MSG_116=ble_config adv 200
MSG_116_SEL=0
MSG_116_DELAY=
MSG_116_TYPE=

MSG_117=ble_config adv 500
MSG_117_SEL=0
MSG_117_DELAY=
MSG_117_TYPE=

MSG_118=ble_config adv 1000
MSG_118_SEL=0
MSG_118_DELAY=
MSG_118_TYPE=

MSG_119=ble_config adv 2000
MSG_119_SEL=0
MSG_119_DELAY=
MSG_119_TYPE=

MSG_120=ble_config conn 50
MSG_120_SEL=0
MSG_120_DELAY=
MSG_120_TYPE=

MSG_121=ble_config conn 100
MSG_121_SEL=0
MSG_121_DELAY=
MSG_121_TYPE=

MSG_122=ble_config conn 200
MSG_122_SEL=0
MSG_122_DELAY=
MSG_122_TYPE=

MSG_123=ble_config conn 500
MSG_123_SEL=0
MSG_123_DELAY=
MSG_123_TYPE=

MSG_124=ble_config conn 1000
MSG_124_SEL=0
MSG_124_DELAY=
MSG_124_TYPE=

MSG_125=ble_config conn 2000
MSG_125_SEL=0
MSG_125_DELAY=
MSG_125_TYPE=

MSG_126=
MSG_126_SEL=0
MSG_126_DELAY=
MSG_126_TYPE=

MSG_127=<< coremark run cmd >>
MSG_127_SEL=0
MSG_127_DELAY=
MSG_127_TYPE=

MSG_128=run_coremark 240
MSG_128_SEL=0
MSG_128_DELAY=
MSG_128_TYPE=

MSG_129=run_coremark 192
MSG_129_SEL=0
MSG_129_DELAY=
MSG_129_TYPE=

MSG_130=run_coremark 48
MSG_130_SEL=0
MSG_130_DELAY=
MSG_130_TYPE=

MSG_131=run_coremark 24
MSG_131_SEL=0
MSG_131_DELAY=
MSG_131_TYPE=

MSG_132=run_while_loop 240
MSG_132_SEL=0
MSG_132_DELAY=
MSG_132_TYPE=

MSG_133=run_while_loop 192
MSG_133_SEL=0
MSG_133_DELAY=
MSG_133_TYPE=

MSG_134=run_while_loop 48
MSG_134_SEL=0
MSG_134_DELAY=
MSG_134_TYPE=

MSG_135=run_while_loop 24
MSG_135_SEL=0
MSG_135_DELAY=
MSG_135_TYPE=

MSG_136=
MSG_136_SEL=0
MSG_136_DELAY=
MSG_136_TYPE=

MSG_137=<< app run cmd >>
MSG_137_SEL=0
MSG_137_DELAY=
MSG_137_TYPE=

MSG_138=app_run Main
MSG_138_SEL=0
MSG_138_DELAY=
MSG_138_TYPE=

MSG_139=app_run Guide
MSG_139_SEL=0
MSG_139_DELAY=
MSG_139_TYPE=

MSG_140=app_run Clock
MSG_140_SEL=0
MSG_140_DELAY=
MSG_140_TYPE=

MSG_141=app_run Setting
MSG_141_SEL=0
MSG_141_DELAY=
MSG_141_TYPE=

MSG_142=app_run Switch_anim
MSG_142_SEL=0
MSG_142_DELAY=
MSG_142_TYPE=

MSG_143=app_run Blood_oxygen
MSG_143_SEL=0
MSG_143_DELAY=
MSG_143_TYPE=

MSG_144=app_run Heart_rate
MSG_144_SEL=0
MSG_144_DELAY=
MSG_144_TYPE=

MSG_145=app_run Weather
MSG_145_SEL=0
MSG_145_DELAY=
MSG_145_TYPE=

MSG_146=app_run Take_picture
MSG_146_SEL=0
MSG_146_DELAY=
MSG_146_TYPE=

MSG_147=app_run Stopwatch
MSG_147_SEL=0
MSG_147_DELAY=
MSG_147_TYPE=

MSG_148=app_run Calendar
MSG_148_SEL=0
MSG_148_DELAY=
MSG_148_TYPE=

MSG_149=app_run Calculator
MSG_149_SEL=0
MSG_149_DELAY=
MSG_149_TYPE=

MSG_150=app_run Sport
MSG_150_SEL=0
MSG_150_DELAY=
MSG_150_TYPE=

MSG_151=app_run Sport_tlv
MSG_151_SEL=0
MSG_151_DELAY=
MSG_151_TYPE=

MSG_152=app_run Sport_rcd_tlv
MSG_152_SEL=0
MSG_152_DELAY=
MSG_152_TYPE=

MSG_153=app_run Message
MSG_153_SEL=0
MSG_153_DELAY=
MSG_153_TYPE=

MSG_154=app_run Alarm
MSG_154_SEL=0
MSG_154_DELAY=
MSG_154_TYPE=

MSG_155=app_run Sleep
MSG_155_SEL=0
MSG_155_DELAY=
MSG_155_TYPE=

MSG_156=app_run Music
MSG_156_SEL=0
MSG_156_DELAY=
MSG_156_TYPE=

MSG_157=app_run Compass
MSG_157_SEL=0
MSG_157_DELAY=
MSG_157_TYPE=

MSG_158=app_run BT_pair
MSG_158_SEL=0
MSG_158_DELAY=
MSG_158_TYPE=

MSG_159=app_run Activity
MSG_159_SEL=0
MSG_159_DELAY=
MSG_159_TYPE=

MSG_160=app_run Breathe
MSG_160_SEL=0
MSG_160_DELAY=
MSG_160_TYPE=

MSG_161=app_run Phonecall
MSG_161_SEL=0
MSG_161_DELAY=
MSG_161_TYPE=

MSG_162=app_run Always_on
MSG_162_SEL=0
MSG_162_DELAY=
MSG_162_TYPE=

MSG_163=app_run Notify_subpage
MSG_163_SEL=0
MSG_163_DELAY=
MSG_163_TYPE=

MSG_164=app_run Alipay
MSG_164_SEL=0
MSG_164_DELAY=
MSG_164_TYPE=

MSG_165=app_run Countdown
MSG_165_SEL=0
MSG_165_DELAY=
MSG_165_TYPE=

MSG_166=app_run Factory_mode
MSG_166_SEL=0
MSG_166_DELAY=
MSG_166_TYPE=

MSG_167=app_run Dice
MSG_167_SEL=0
MSG_167_DELAY=
MSG_167_TYPE=

MSG_168=app_run Plane
MSG_168_SEL=0
MSG_168_DELAY=
MSG_168_TYPE=

MSG_169=app_cleanup
MSG_169_SEL=0
MSG_169_DELAY=
MSG_169_TYPE=

MSG_170=
MSG_170_SEL=0
MSG_170_DELAY=
MSG_170_TYPE=

MSG_171=<< utest run cmd >>
MSG_171_SEL=0
MSG_171_DELAY=
MSG_171_TYPE=

MSG_172=utest_list
MSG_172_SEL=0
MSG_172_DELAY=
MSG_172_TYPE=

MSG_173=utest_run example_dma 1
MSG_173_SEL=0
MSG_173_DELAY=
MSG_173_TYPE=

MSG_174=utest_run example_psram 1
MSG_174_SEL=0
MSG_174_DELAY=
MSG_174_TYPE=

MSG_175=utest_run tc_hal_epic 1
MSG_175_SEL=0
MSG_175_DELAY=
MSG_175_TYPE=

MSG_176=utest_run tc_drv_flash 1
MSG_176_SEL=0
MSG_176_DELAY=
MSG_176_TYPE=

MSG_177=utest_run tc_drv_spi_inter 1
MSG_177_SEL=0
MSG_177_DELAY=
MSG_177_TYPE=

MSG_178=utest_run tc_drv_spi_aide 1
MSG_178_SEL=0
MSG_178_DELAY=
MSG_178_TYPE=

MSG_179=utest_run tc_drv_spi_inter 1 spi3;master;12000000;0;w;300;8;0x5;4;1
MSG_179_SEL=0
MSG_179_DELAY=
MSG_179_TYPE=

MSG_180=utest_run tc_sdmmc 1 -a
MSG_180_SEL=0
MSG_180_DELAY=
MSG_180_TYPE=

MSG_181=utest_run tc_hal_atomic 1
MSG_181_SEL=0
MSG_181_DELAY=
MSG_181_TYPE=

MSG_182=lcpu on
MSG_182_SEL=0
MSG_182_DELAY=
MSG_182_TYPE=

MSG_183=
MSG_183_SEL=0
MSG_183_DELAY=
MSG_183_TYPE=

MSG_184=<< app SIM CMD >>
MSG_184_SEL=0
MSG_184_DELAY=
MSG_184_TYPE=

MSG_185=sync_step
MSG_185_SEL=0
MSG_185_DELAY=
MSG_185_TYPE=

MSG_186=sync_hr
MSG_186_SEL=0
MSG_186_DELAY=
MSG_186_TYPE=

MSG_187=sync_sport
MSG_187_SEL=0
MSG_187_DELAY=
MSG_187_TYPE=

MSG_188=sync_sedentary
MSG_188_SEL=0
MSG_188_DELAY=
MSG_188_TYPE=

MSG_189=sync_drink
MSG_189_SEL=0
MSG_189_DELAY=
MSG_189_TYPE=

MSG_190=sync_battery
MSG_190_SEL=0
MSG_190_DELAY=
MSG_190_TYPE=

MSG_191=sync_sleep
MSG_191_SEL=0
MSG_191_DELAY=
MSG_191_TYPE=

MSG_192=photograph
MSG_192_SEL=0
MSG_192_DELAY=
MSG_192_TYPE=

MSG_193=find_phone
MSG_193_SEL=0
MSG_193_DELAY=
MSG_193_TYPE=

MSG_194=
MSG_194_SEL=0
MSG_194_DELAY=
MSG_194_TYPE=

MSG_195=<< audio dump data >>
MSG_195_SEL=0
MSG_195_DELAY=
MSG_195_TYPE=

MSG_196=audio_data -downlink -downlink_agc -dc_out -audprc
MSG_196_SEL=0
MSG_196_DELAY=
MSG_196_TYPE=

MSG_197=audio_data -stop
MSG_197_SEL=0
MSG_197_DELAY=
MSG_197_TYPE=

MSG_198=
MSG_198_SEL=0
MSG_198_DELAY=
MSG_198_TYPE=

