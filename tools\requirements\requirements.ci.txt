# Python package requirements for CI in SiFli-SDK.
# This feature can be enabled by running "install.{sh,bat,ps1,fish} --enable-ci"
#
# This file lists Python packages without version specifiers. Version details
# are stored in a separate constraints file. For more information, visit:
# https://docs.espressif.com/projects/esp-idf/en/latest/api-guides/tools/idf-tools.html

# ci
coverage
idf-build-apps
jsonschema
junit_xml
python-gitlab
pyyaml
SimpleWebSocketServer
pylint-gitlab
minio
prettytable
