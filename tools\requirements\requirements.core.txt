# Python package requirements for SiFli-SDK. These are the so called core features which are installed in all systems.
#
# This file lists Python packages without version specifiers. Version details
# are stored in a separate constraints file. For more information, visit:
# https://docs.espressif.com/projects/esp-idf/en/latest/api-guides/tools/idf-tools.html

setuptools
packaging
# importlib_metadata: is part of python3.8 and newer as importlib.metadata
importlib_metadata; python_version < "3.8"
requests
click
pyserial
cryptography
pyparsing
pyelftools
pyclang
construct
rich
psutil
scons
esp-idf-kconfig
pycryptodome