version: '1.0'

# 通用编译参数
common:
  compiler: scons
  parallel_jobs: 8
  log_dir: ci_build_logs
  report_dir: ci_report_logs
  backup_dir: ci_backup_builds

# 普通项目配置
#projects:
#  - group: ble
#    path:
#      - example/ble/ancs_dualcore/project/eh-lb555/lcpu
#      - example/ble/ancs_dualcore/project/eh-lb555/hcpu
#      - example/ble/ancs_dualcore/project/eh-ss6600_551/lcpu
#      - example/ble/ancs_dualcore/project/eh-ss6600_551/hcpu


# 通用项目配置
default_boards: &default_boards
  - ec-lb567_hcpu
  - eh-lb563_hcpu
  - eh-lb561_hcpu
  - eh-lb525_hcpu
  - eh-lb523_hcpu
  - eh-lb520_hcpu
  - eh-lb551_hcpu
  - eh-lb555_hcpu
  - ec-lb587_hcpu
  - ec-lb585_hcpu
  - ec-lb583_hcpu
  - sf32lb52-lcd_n16r8
  - sf32lb52-lcd_52d
  - sf32lb56-lcd_a128r12n1
  - sf32lb56-lcd_n16r12n1
  - sf32lb58-lcd_n16r64n4

bt_boards: &bt_boards
  - ec-lb567_hcpu
  - eh-lb563_hcpu
  - eh-lb561_hcpu
  - eh-lb525_hcpu
  - eh-lb523_hcpu
  - eh-lb520_hcpu
  - ec-lb587_hcpu
  - ec-lb585_hcpu
  - ec-lb583_hcpu
  - sf32lb52-lcd_n16r8 
  - sf32lb52-lcd_52d
  - sf32lb56-lcd_a128r12n1
  - sf32lb56-lcd_n16r12n1
  - sf32lb58-lcd_n16r64n4

multicore_boards: &multicore_boards
  - ec-lb567_hcpu
  - eh-lb563_hcpu
  - eh-lb561_hcpu
  - eh-lb551_hcpu
  - eh-lb555_hcpu
  - ec-lb587_hcpu
  - ec-lb585_hcpu
  - ec-lb583_hcpu
  - sf32lb56-lcd_a128r12n1
  - sf32lb56-lcd_n16r12n1
  - sf32lb58-lcd_n16r64n4

after_55x_boards: &after_55x_boards
  - ec-lb567_hcpu
  - eh-lb563_hcpu
  - eh-lb561_hcpu
  - eh-lb525_hcpu
  - eh-lb523_hcpu
  - ec-lb587_hcpu
  - ec-lb583_hcpu
  - ec-lb585_hcpu
  - sf32lb52-lcd_n16r8 
  - sf32lb52-lcd_52d
  - sf32lb56-lcd_a128r12n1
  - sf32lb56-lcd_n16r12n1
  - sf32lb58-lcd_n16r64n4

525_devkit_lcd_boards: &525_devkit_lcd_boards
  - sf32lb52-lcd_n16r8 


common_projects:
  - path: example/ble/ams/project
    boards: *default_boards

  - path: example/ble/central_and_peripheral/project/hcpu
    boards: *default_boards

  - path: example/ble/hid/project
    boards: *default_boards

  - path: example/ble/multi_connection/project/common
    boards: *default_boards

  - path: example/ble/periodic_adv/project
    boards: *default_boards

  - path: example/ble/periodic_adv_sync/project
    boards: *default_boards

  - path: example/ble/peripheral/project
    boards: *default_boards

  - path: example/ble/peripheral_with_ota/project
    boards: *after_55x_boards

  - path: example/ble/throughput/project
    boards: *default_boards

  - path: example/bt/HCI_over_uart/project
    boards: 
      - sf32lb52-lcd_n16r8_hcpu    

  - path: example/bt/hfp/project
    boards: *bt_boards

  - path: example/bt/music_sink/project
    boards: *bt_boards

  - path: example/bt/music_source/project
    boards: *bt_boards

  - path: example/bt/pan/project
    boards: *bt_boards

  - path: example/bt/spp/project
    boards: 
      - sf32lb58-lcd_n16r64n4

  - path: example/bt/test_example/project
    boards: *bt_boards

  - path: example/get-started/blink/rtt/project
    boards: *default_boards

  - path: example/get-started/blink/no-os/project
    boards: *default_boards

  - path: example/get-started/hello_world/no-os/project
    boards: *default_boards

  - path: example/get-started/hello_world/rtt/project
    boards: *default_boards

  - path: example/hal/adc/adc_battery/project
    boards: *525_devkit_lcd_boards

  - path: example/hal/aes/project
    boards: *525_devkit_lcd_boards

  - path: example/hal/crc/project
    boards: *525_devkit_lcd_boards

  - path: example/hal/ezip/project
    boards: *525_devkit_lcd_boards

  - path: example/hal/gpio/project
    boards: *525_devkit_lcd_boards

  - path: example/hal/hash/project
    boards: *525_devkit_lcd_boards
  
  - path: example/hal/hwtimer/project
    boards: *525_devkit_lcd_boards
  
  - path: example/hal/i2c/eeprom/project
    boards: *525_devkit_lcd_boards

  - path: example/hal/pwm/project
    boards: *525_devkit_lcd_boards

  - path: example/hal/rng/project
    boards: *525_devkit_lcd_boards

  - path: example/hal/rtc/project
    boards: *525_devkit_lcd_boards

  - path: example/hal/spi/project
    boards: *525_devkit_lcd_boards

  - path: example/hal/tsen/project
    boards: *525_devkit_lcd_boards

  - path: example/hal/uart/project
    boards: *525_devkit_lcd_boards

  - path: example/hal/wdt/iwdt/project
    boards: *525_devkit_lcd_boards

  - path: example/hal/wdt/wdt1/project
    boards: *525_devkit_lcd_boards

  - path: example/hal_example/project
    boards: *default_boards

  - path: example/misc/button/project
    boards: *525_devkit_lcd_boards

  - path: example/multicore/rpmsg-lite/project/hcpu
    boards: *multicore_boards

  - path: example/multicore/data_service/project/hcpu
    boards: *multicore_boards

  - path: example/multicore/ipc_queue/project/hcpu
    boards: *multicore_boards

  - path: example/multimedia/audio/local_music/project
    boards: *after_55x_boards

  - path: example/multimedia/audio/record/project
    boards: *after_55x_boards

  - path: example/multimedia/lvgl/lvgl_v8_demos/project
    boards: *default_boards

  - path: example/multimedia/lvgl/lvgl_v8_examples/project
    boards: *default_boards

  # - path: example/multimedia/lvgl/lvgl_v8_media/project
  #   boards: *after_55x_boards
  - path: example/multimedia/lvgl/lvgl_v9_demos/project
    boards: 
      - ec-lb567_hcpu
      - eh-lb563_hcpu
      - eh-lb561_hcpu
      - eh-lb525_hcpu
      - eh-lb523_hcpu
      - eh-lb551_hcpu
      - eh-lb555_hcpu
      - ec-lb587_hcpu
      - ec-lb585_hcpu
      - ec-lb583_hcpu
      - sf32lb52-lcd_n16r8 
      - sf32lb56-lcd_a128r12n1
      - sf32lb56-lcd_n16r12n1
      - sf32lb58-lcd_n16r64n4

  - path: example/multimedia/lvgl/lvgl_v9_examples/project
    boards: 
      - ec-lb567_hcpu
      - eh-lb563_hcpu
      - eh-lb561_hcpu
      - eh-lb525_hcpu
      - eh-lb523_hcpu
      - eh-lb551_hcpu
      - eh-lb555_hcpu
      - ec-lb587_hcpu
      - ec-lb585_hcpu
      - ec-lb583_hcpu
      - sf32lb52-lcd_n16r8 
      - sf32lb56-lcd_a128r12n1
      - sf32lb56-lcd_n16r12n1
      - sf32lb58-lcd_n16r64n4

  - path: example/multimedia/lvgl/watch/project
    boards:
      - eh-lb551_hcpu
      - eh-lb555_hcpu
      - ec-lb567_hcpu
      - eh-lb563_hcpu
      - eh-lb561_hcpu
      - eh-lb525_hcpu
      - eh-lb523_hcpu
      - ec-lb587_hcpu
      - ec-lb585_hcpu
      - ec-lb583_hcpu
      - sf32lb52-lcd_n16r8 
      - sf32lb56-lcd_a128r12n1
      - sf32lb56-lcd_n16r12n1
      - sf32lb58-lcd_n16r64n4

  - path: example/pm/ble/project/hcpu
    boards:
      - eh-lb551_hcpu
      - eh-lb555_hcpu

  - path: example/pm/bt/project/hcpu
    boards: *bt_boards

  - path: example/pm/coremark/project/hcpu
    boards: *default_boards

  # - path: example/printer/project
  #   boards:
  #     - eh-lb520s

  - path: example/rt_device/adc/adc_battery/project
    boards: *525_devkit_lcd_boards

  - path: example/rt_device/audprc/project
    boards: *after_55x_boards

  - path: example/rt_device/gpio/project
    boards: *525_devkit_lcd_boards

  - path: example/rt_device/i2c/eeprom/project
    boards: *525_devkit_lcd_boards

  - path: example/rt_device/i2s/project
    boards: *525_devkit_lcd_boards

  - path: example/rt_device/pdm/project
    boards: *525_devkit_lcd_boards

  - path: example/rt_device/pm/project/hcpu
    boards:
      - sf32lb56-lcd_n16r12n1
      # - sf32lb56-lcd_a128r12n1
      - eh-lb551
      - sf32lb52-lcd_n16r8
      - sf32lb58-lcd_n16r64n4

  - path: example/rt_device/pwm/pwm/project
    boards: *525_devkit_lcd_boards

  - path: example/rt_device/pwm/pwm_dma/project
    boards: *525_devkit_lcd_boards

  - path: example/rt_device/rgb_led/project
    boards: *525_devkit_lcd_boards

  - path: example/rt_device/rtc/project
    boards: *525_devkit_lcd_boards

  - path: example/rt_device/spi/card/project
    boards: *525_devkit_lcd_boards

  - path: example/rt_device/spi_tf/project
    boards: *525_devkit_lcd_boards

  - path: example/rt_device/uart/project
    boards: *525_devkit_lcd_boards

  - path: example/rt_device/usb/usb_mstorage/project
    boards: *525_devkit_lcd_boards

  - path: example/rt_device/usb/usb_vcom/project
    boards: *525_devkit_lcd_boards

  - path: example/rt_device/wdt/project
    boards: *525_devkit_lcd_boards    

  - path: example/rt_driver/project
    boards: *default_boards

  - path: example/storage/fatfs/nand/project
    boards: *default_boards

  - path: example/storage/fatfs/nor/project
    boards: *default_boards

  - path: example/storage/flashdb/project/nand
    boards:
      - eh-lb525

  - path: example/storage/flashdb/project/nor
    boards:
      - eh-lb523
      - sf32lb52-lcd_n16r8 

  - path: example/uart/project
    boards: *default_boards

