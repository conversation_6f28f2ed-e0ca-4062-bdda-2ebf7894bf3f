# Python package requirements for building the documentation of SiFli-SDK.
# This feature can be enabled by running "install.{sh,bat,ps1,fish} --enable-docs"
#
# This file lists Python packages without version specifiers. Version details
# are stored in a separate constraints file. For more information, visit:
# https://docs.espressif.com/projects/esp-idf/en/latest/api-guides/tools/idf-tools.html

esp-docs
linuxdoc
