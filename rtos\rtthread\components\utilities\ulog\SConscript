from building import *

cwd  = GetCurrentDir()
src  = Glob('*.c')
path = [cwd, os.path.join(cwd, 'backend')]

if GetDepend('ULOG_BACKEND_USING_CONSOLE'):
    src += ['backend/console_be.c']

if GetDepend('ULOG_BACKEND_USING_TSDB'):
    src += ['backend/tsdb_be.c']

if GetDepend('ULOG_BACKEND_USING_RAM'):
    src += ['backend/ram_be.c']

if GetDepend('SAVE_ASSERT_CONTEXT_IN_FLASH'):
    src += ['backend/assert_context_be.c']

if GetDepend('ULOG_USING_SYSLOG'):
    path +=  [cwd + '/syslog']
    src  += Glob('syslog/*.c')

group = DefineGroup('Utilities', src, depend = ['RT_USING_ULOG'], CPPPATH = path)

Return('group')
