【2.2.3】
1)回放log可以选择多个文件
2)双击列表可以选择性复制信息
3)解决上个版本数据量大时闪退的问题

【2.2.2】
1)优化消息显示，使用虚拟列表控件
2)在用户搜索界面添加几种消息颜色设置功能

【2.2.1】
1)搜索框添加Ctrl+A  Ctrl+C  Ctrl+S 快捷键用于保存
2)删除UI字符多余的 \r \n

【2.2.0】
1)添加工具免责声明
2)添加framebuf转换工具，以及FsrwTool支持导出framebuf
3)解决占用CPU多的bug

【2.1.9】
1)添加脚本调用线程，将部分工具放到本工具路径，方便使用

【2.1.8】
1)常用finsh命令整理
2)增加解析带有HCI数据的log回放
3)将串口dump/52x串口debug server/串口文件读写等工具放到一起，方便使用

【2.1.7】
1)添加抓取音频数据时漏数的打印
2)优化新加入或者删除发送命令速度慢的问题

【2.1.6】
1)添加抓取音频数据的功能

【2.1.5】
1)添加HCI数据是否再界面显示控制按钮。
2)去除log描述编辑框
3)解决关闭显示不保存数据问题，解决搜索问题

【2.1.4】
1)添加监控外部命令，以停止某个串口再打开，避免多个软件共用一个串口打开异常的问题。

【2.1.3】
1)搜索界面添加不区分大小写的功能。
2)屏蔽HCI数据打印。

【2.1.2】
1) 添加52X同debug server交互，支持调试和TRACE，选择UART1_SOCK或UART4_SOCK。

【2.1.1】
1) 解决部分场景下保存文件为宽字符的问题。
2) 对于SEGGER_RTT跟踪方式，ini中提供支持指定地址以及范围配置方式，避免工具中写死搜索范围导致的连接慢的问题。

【2.1.0】
1) support search SEGGER_RTT addr if the SEGGERCFG_ADDR not be set in ini, but it will use long time in first time。

【2.0.9】
1) 控制进入bootmode的寄存器地址变化，0x5000b000 -> 0xffa57200。

【2.0.8】
1) 添加54X芯片，bootrom启动发送 SFBL\n，工具打开 BOOT 开关，就会设置BOOT_MODE寄存器，就不会往二级bootloader跳转。

【2.0.7】
1) 查询端口显示发送消息不过修复，增加监控信息功能。
2) 界面显示优化。

【2.0.6】
1) trace窗口添加了命令输入控件，方便常用命令交互，最多缓存20条常用命令，可通过上下方向按键查找。
2) 解决端口脱落后更换其他端口无法连接问题。

【2.0.5】
1) 增加了log立即保存按钮，点击可立即保存当前缓存log。
2) trace窗口自动调整大小，新增/关闭窗口，调整发送窗口时会触发重排序

【2.0.4】
1) 增加jlink rtt作为trace端口，在端口中直接选择jlink sn号即可