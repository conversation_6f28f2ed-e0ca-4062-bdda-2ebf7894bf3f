;Brief:  Parse thread's SP and overwrite CPU registers.
;Useage: do switch_to #thread_sp_address#



ENTRY &thread_sp_addr

PRIVATE &is_fpu_stack
&is_fpu_stack=1




   IF &is_fpu_stack==1
   (
        PRIVATE &exception_stack_sp_with_fpu_addr

		//Skip flag,r4 ~ r11 register,FPU register s16 ~ s31
		&exception_stack_sp_with_fpu_addr=&thread_sp_addr


		; set sp 
		r.s r13 var.value(&exception_stack_sp_with_fpu_addr+sizeof(struct exception_stack_frame_fpu))
		r.s r0 var.value(((struct exception_stack_frame_fpu *)&exception_stack_sp_with_fpu_addr)->r0)
		r.s r1 var.value(((struct exception_stack_frame_fpu *)&exception_stack_sp_with_fpu_addr)->r1)
		r.s r2 var.value(((struct exception_stack_frame_fpu *)&exception_stack_sp_with_fpu_addr)->r2)
		r.s r3 var.value(((struct exception_stack_frame_fpu *)&exception_stack_sp_with_fpu_addr)->r3)
		r.s r12 var.value(((struct exception_stack_frame_fpu *)&exception_stack_sp_with_fpu_addr)->r12)
		; set lr
		r.s r14 var.value(((struct exception_stack_frame_fpu *)&exception_stack_sp_with_fpu_addr)->lr) 
		r.s pc var.value(((struct exception_stack_frame_fpu *)&exception_stack_sp_with_fpu_addr)->pc)
		r.s xpsr var.value(((struct exception_stack_frame_fpu *)&exception_stack_sp_with_fpu_addr)->psr)
   )
   ELSE
   (
        PRIVATE &exception_stack_sp_addr
	
		//Skip flag,r4 ~ r11 register
		&exception_stack_sp_addr=&thread_sp_addr
		
		; set sp 
		r.s r13 var.value(&exception_stack_sp_addr+sizeof(struct exception_stack_frame))
		r.s r0 var.value(((struct exception_stack_frame *)&exception_stack_sp_addr)->r0)
		r.s r1 var.value(((struct exception_stack_frame *)&exception_stack_sp_addr)->r1)
		r.s r2 var.value(((struct exception_stack_frame *)&exception_stack_sp_addr)->r2)
		r.s r3 var.value(((struct exception_stack_frame *)&exception_stack_sp_addr)->r3)
		r.s r12 var.value(((struct exception_stack_frame *)&exception_stack_sp_addr)->r12)
		; set lr
		r.s r14 var.value(((struct exception_stack_frame *)&exception_stack_sp_addr)->lr) 
		r.s pc var.value(((struct exception_stack_frame *)&exception_stack_sp_addr)->pc)
		r.s xpsr var.value(((struct exception_stack_frame *)&exception_stack_sp_addr)->psr)
   )


