# Python package requirements for gdbgui support SiFli-SDK.
# This feature can be enabled by running "install.{sh,bat,ps1,fish} --enable-gdbgui"
#
# This file lists Python packages without version specifiers. Version details
# are stored in a separate constraints file. For more information, visit:
# https://docs.espressif.com/projects/esp-idf/en/latest/api-guides/tools/idf-tools.html

# gdbgui Python 3.11 issue https://github.com/cs01/gdbgui/issues/447 was fixed in 0.15.2.0. Windows users need an
# older Python to use since new gdbgui versions don't support Windows anymore.
# Python 3.13 is not supported: https://github.com/cs01/gdbgui/issues/494
gdbgui; sys_platform != 'win32' and python_version < "3.13"
gdbgui; sys_platform == 'win32' and python_version < "3.11"
