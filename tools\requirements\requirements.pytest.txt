# Python package requirements for pytest in SiFli-SDK.
# This feature can be enabled by running "install.{sh,bat,ps1,fish} --enable-pytest"
#
# This file lists Python packages without version specifiers. Version details
# are stored in a separate constraints file. For more information, visit:
# https://docs.espressif.com/projects/esp-idf/en/latest/api-guides/tools/idf-tools.html

pytest-embedded-serial-esp
pytest-embedded-idf
pytest-embedded-jtag
pytest-embedded-qemu
pytest-rerunfailures
pytest-timeout
pytest-ignore-test-results

# ci
minio

# build
python-gitlab
idf-build-apps
