#define BOOT_CONFIG_FLASH_ADDR  0x10000000
#define FACTORY_CAL_TABLE_ADDR  0x10005000

#define BOOTLOADER_PATCH_FLASH_ADDR  0x10010000
#define BOOTLOADER_PATCH_CODE_ADDR   0x20050000       // Bootloader Patch code in RAM

#define HCPU_CODE_FLASH_ADDR_1	0x10020000
#define HCPU_CODE_FLASH_ADDR_2	HCPU_CODE_FLASH_ADDR_1

#define HCPU_CODE_ADDR    HCPU_CODE_FLASH_ADDR_1


{
    "name":"Flash table",
    "num":14,
    "tables": [
    	// Secure config
        {"base":BOOT_CONFIG_FLASH_ADDR, "size":0x5000,    "xip_base":0,     "flags":0},
        {"base":FACTORY_CAL_TABLE_ADDR, "size":0x2000,    "xip_base":0,     "flags":0},

        // Ping images
        {"base":0,  "size":0,  "xip_base":0,  "flags":0},
        {"base":0,  "size":0,  "xip_base":0,  "flags":0},
        {"base":HCPU_CODE_FLASH_ADDR_1,      "size":0x80000,   "xip_base":HCPU_CODE_ADDR,             "flags":0},
        {"base":BOOTLOADER_PATCH_FLASH_ADDR, "size":0x10000,   "xip_base":BOOTLOADER_PATCH_CODE_ADDR, "flags":0},

      	// Pong images
        {"base":0,  "size":0,   "xip_base":0,   "flags":0},
        {"base":0,  "size":0,   "xip_base":0,   "flags":0},
        {"base":HCPU_CODE_FLASH_ADDR_2,      "size":0x80000,   "xip_base":HCPU_CODE_ADDR,             "flags":0},
        {"base":BOOTLOADER_PATCH_CODE_ADDR,  "size":0x10000,   "xip_base":BOOTLOADER_PATCH_CODE_ADDR, "flags":0},

      	// HCPU ext
        {"base":0,  "size":0, "xip_base":0,   "flags":0},
        {"base":0,  "size":0, "xip_base":0,   "flags":0},	

        // LCPU ext
        {"base":0,  "size":0, "xip_base":0,   "flags":0},
        {"base":0,  "size":0, "xip_base":0,   "flags":0},		
    ],
}
